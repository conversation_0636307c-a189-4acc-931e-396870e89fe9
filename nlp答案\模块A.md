模块A、自然语言处理技术的应用
一、任务要求
根据项目要求完成人工智能自然语言处理技术应用代码开发。将“nlp.ipynb”中填写的代码截图并粘贴在“answer.doc”的指定位置中。最后按照《选手指引》要求将上述文件及任务明确要求保存的模型保存到指定位置。
二、任务环境
硬件资源：
软件资源：“NLP.zip”，pytorch深度学习框架。
三、任务一说明（命名实体提取“NER”）（共20分）
命名实体识别作为自然语言处理的基础任务，旨在从文本中识别并分类具有特定意义的实体，如人名、地名、机构名等，是信息抽取与语义理解的关键环节。文本中蕴含的实体信息往往是构建知识库的核心节点，通过精准识别这些实体，能够为文本内容建立结构化的语义锚点，帮助机器理解文本的深层含义。通过准确的命名实体识别，可以为医学研究、临床实践和医疗信息管理提供有价值的支持。对于医学研究者来说，能够从海量的医学文献中快速准确地提取出关键的实体信息，有助于深入研究疾病的发病机制、药物的疗效和安全性等问题。在临床实践中，医生可以利用命名实体识别技术从患者的病历、检查报告等文本中提取重要信息，辅助诊断和治疗决策。同时，命名实体识别也可以为医疗信息管理系统提供基础数据，实现对医学信息的高效组织和检索。
点击“NER”代码文件夹，进入到任务训练场景中。文件提供了所需的全部任务文件。其中“/data”为训练、测试所用的数据，*.py文件为模型训练评估的python代码文件，请根据这相应文件中的已给内容编写代码完成以下操作：
1.数据处理（convert2bioes.py）（4分）
任务中已给出标注好的命名实体数据，并划分为train、dev、和test数据集保存在/data文件夹内。先需要在convert2bioes.py文件中编写python代码，将JSON格式的数据转化为BIOES格式的标准命名实体标注数据，并将转化后的BIOES数据保存为train.bioes、dev.bioes、和test.bioes文件。具体格式如下：
字母	含义	举例
B	Begin，实体的开头	B-PER表示人名开头
I	Inside，实体的中间部分	I-PER表示人名中间
O	Outside，不属于任何实体	O就是背景词
E	End，实体的结尾	E-PER表示人名结尾
S	Single，单字实体	S-ORG表示一个词就是一个组织名
示例请参照train_sample.bioes文件。
（1）步骤1.转换文件格式（1分）
部分代码已给出，请根据提示，将代码补充完整。
定义split_sentences函数，该函数的任务是根据特定标点符号分割文本为句子列表。待分割的文本为string类型，分割后的句子保存到列表中。将<1>处补充的代码截图并保存在“answer.doc”中。
（2）步骤2.根据JSON格式数据划分BIOES标注数据（1分）
部分代码已给出，请根据提示，将代码补充完整。
定义process_entity函数，根据entity类别属性，定义BIOES标签。分别对单字实体和多字实体进行处理。将<2>处补充的代码截图并保存在“answer.doc”中。
（3）步骤3.根据特定标点符号分割文本为句子列表（1分）
部分代码已给出，请根据提示，将代码补充完整。
定义convert_to_bioes函数，该函数的任务是将JSON格式的数据转换成BIOES标注格式。输入数据为包含段落和实体信息的字典结构，输出为BIOES格式的行列表。请根据JSON数据格式，提取文本中的段落、句子、实体。并使用（2）中的函数生成BOIES行列表。将<3>处补充的代码截图并保存在“answer.doc”中。
（4）步骤4.完成格式转变（1分）
部分代码已给出，请根据提示，将代码补充完整。
处理目录中的所有JSON文件，转换为BIOES格式。Input_dir为输入目录路径，所有文件的BIOES格式行列表。请使用（3）中定义的函数遍历目录中的所有JSON文件生成最终BOIES行列表。将<4>处补充的代码截图并保存在“answer.doc”中。将上述步骤的输出结果进行截图并保存在“/data”路径下。
2.命名实体识别（16分）
（1）步骤1.构建模型输入数据(data.py)（1分，每题0.5分）
部分代码已给出，请根据提示，将代码补充完整。
①定义build_corpus函数，该函数需要读出train、dev、和test三个bioes格式的文件内容，生成词列表、标签列表。将<1>处补充的代码截图并保存在“answer.doc”中。
②同样在build_corpus函数中，如果布尔型参数make_vocab为True，还需要生成单词与编号对应的map，以及标签与编号对应的map。请在<2>处补充代码，并将<2>处补充的代码截图并保存在“answer.doc”中。
（2）步骤2.基于条件随机场（CRF）的序列标注模型(/models/crf.py)（2分，每题1分）
部分代码已给出，请根据提示，将代码补充完整。
①编写训练函数，使用util.py中适当的函数提取语句特征，并将特征向量和标签列表作为输入训练模型。将<1>处补充的代码截图并保存在“answer.doc”中。
②编写测试函数，使用util.py中适当的函数提取语句特征，输出模型预测结果。将<2>处补充的代码截图并保存在“answer.doc”中。
（3）步骤3.基于双向LSTM（BiLSTM）的序列标注模型(/models/bilstm.py)（2分，每题0.5分）
部分代码已给出，请根据提示，将代码补充完整。
①根据词典大小和词向量维度，定义词向量。将<1>处补充的代码截图并保存在“answer.doc”中。
②定义BiLSTM模型，补全输入维度、隐藏层维度、batch_first（True）、和双向LSTM的设置。将<2>处补充的代码截图并保存在“answer.doc”中。
③定义输出全连接层，补全输入维度（对应BiLSTM层的hidden_states）、输出维度的设置。将<3>处补充的代码截图并保存在“answer.doc”中。
④编写BiLSTM模型的forward函数。将输入的索引转换为词向量，输入BiLSTM进行编码，然后通过全连接层将输出映射到标签空间，得到每个位置的标签分数。将<4>处补充的代码截图并保存在“answer.doc”中。
（4）步骤4.基于隐马尔可夫模型（HMM）的序列标注模型(/models/hmm.py)（4分，3和4题各1分，其余题各0.5分）
部分代码已给出，请根据提示，将代码补充完整。
①状态转移矩阵表示在HMM中从一个隐状态跳转到另一个隐状态的概率。如果当前处于状态​，那么转移到状态的概率是。根据上述提示完成转移概率矩阵的估计，并对状态转移矩阵的每一行做归一化，使每一行的元素加起来为 1。将<1>处补充的代码截图并保存在“answer.doc”中。
②观测概率矩阵是HMM中用来表示每个隐藏状态生成各个观测值的概率分布，它描述了“某状态下出现某词语”的可能性，即在状态下生成观测符号的概率为。根据该提示完成观测概率矩阵的估计，并对观测概率矩阵的每一行做归一化，使每一行的元素加起来为 1。将<2>处补充的代码截图并保存在“answer.doc”中。
③估计HMM模型中的初始状态概率分布，表示每个隐藏状态作为序列第一个标签出现的概率。根据该原理完成观测概率分布的估计，并对观测概率分布的元素加起来为 1。将<3>处补充的代码截图并保存在“answer.doc”中。
④根据维特比（Viterbi）算法递推公式补全代码。将<4>处补充的代码截图并保存在“answer.doc”中。
⑤从最后一个时间步所有状态中，找到概率最大的那一个状态。该状态对应的是整个最优路径的终点状态，它的概率就是这条路径的总最大概率。根据该提示补全代码。将<5>处补充的代码截图并保存在“answer.doc”中。
⑥从最优路径的终点（最后一个时间步）开始，利用 backpointer 数组逐步追溯每个状态的前驱状态，直到回到起点（第一个时间步）。通过这种方式，得到从起点到终点的完整最优状态路径，并根据回溯顺序拼接这些状态，最终形成最优路径。根据该提示补全代码。将<6>处补充的代码截图并保存在“answer.doc”中。
（5）步骤5.基于双向LSTM和条件随机场（BiLSTM-CRF）的序列标注模型(/models/bilstm_crf.py)（4分，3和6题各1分，其余题各0.5分）
部分代码已给出，请根据提示，将代码补充完整。
①定义模型的transition转移矩阵，大小为（out_size, out_size），并进行均匀初始化。根据上述要求补全代码。将<1>处补充的代码截图并保存在“answer.doc”中。
②通过BiLSTM提取每个字在每个标签下的发射分数，得到发射emission分数。将发射分数与标签间的转移分数相加，获得从标签𝑖转到标签𝑗的总打分。根据上述提示补全代码。将<2>处补充的代码截图并保存在“answer.doc”中。
③Viterbi解码的前向递推阶段，即根据打分一步步推导出每个时间步每个标签的最优路径分数和前驱标签。递推公式为。根据该提示补全代码。将<3>处补充的代码截图并保存在“answer.doc”中。
④补全BiLSTM-CRF的模型定义。将<4>处补充的代码截图并保存在“answer.doc”中。
⑤补全优化器的定义。优化器采用pytorch optim中的Adam。将<5>处补充的代码截图并保存在“answer.doc”中。
⑥补全代码完成模型的前向传播和损失计算。将<6>处补充的代码截图并保存在“answer.doc”中。
（6）步骤6.模型性能指标定义(evaluating.py)（2分，3题1分，其余题各0.5分）
部分代码已给出，请根据提示，将代码补充完整。
简写	全称	含义
TP	True Positive	预测为正类，且实际也是正类
FP	False Positive	预测为正类，但实际是负类
FN	False Negative	预测为负类，但实际是正类
TN	True Negative	预测为负类，且实际也是负类
①计算精确率。可使用类的correct_tags_number函数进行计算。将<1>处补充的代码截图并保存在“answer.doc”中。
②计算召回率分数。可使用类的correct_tags_number函数进行计算。将<2>处补充的代码截图并保存在“answer.doc”中。
③计算模型的F1分数。将<3>处补充的代码截图并保存在“answer.doc”中。
（7）步骤7.模型训练和测试（evaluate.py）（1分，任意答出一个题即给1分）
部分代码已给出，请根据提示，将代码补充完整。
①补全CRF模型的调用和训练代码，和训练后模型储存为“/ckpts”目录下的crf.pkl文件的代码。补全模型的测试和性能指标评估代码。根据上述要求补全代码。将<1>处补充的代码截图并保存在“answer.doc”中。
②补全HMM模型的调用和训练代码，和训练后模型储存为“/ckpts”目录下hmm.pkl文件的代码。补全模型的测试和性能指标评估代码。根据上述要求补全代码。将<2>处补充的代码截图并保存在“answer.doc”中。
③补全BiLSTM模型的调用和训练代码（是否使用CRF作为模型输入参数），和训练后模型储存为“/ckpts”目录下bilstm.pkl或bilstm_crf.pkl文件的代码。补全模型的测试和性能指标评估代码。根据上述要求补全代码。将<3>处补充的代码截图并保存在“answer.doc”中。
注：编写完成后，运行main.py进行模型训练和测试全流程（可以在该文件中注释没有完成的模型）。最后将“./ckpts”中的模型、main.py文件运行结果“main_result.txt”保存到“res”文件夹中。
四、任务二说明（三元组提取“KGC”）（10分）
三元组抽取是自然语言处理的核心任务，旨在从非结构化文本中提取（主体-关系-客体）结构化数据，支撑知识图谱构建。在医疗领域，借助大模型的语言能力，可快速提取疾病-症状-药物关系，辅助临床决策和医学研究。
点击“KGC”代码文件夹，进入到任务训练场景中。文件提供了所需的全部任务文件。其中“/data”为训练、测试所用的数据，*.py文件为模型训练评估的python代码文件，请根据这相应文件中的已给内容编写代码完成以下操作：
1.数据处理（data/raw_data_process.py）（1分）
任务中已给出标注好的数据（CMeIE-V2_test_triples.jsonl），需要先在raw_data_process.py文件中编写python代码，将JSON格式的数据转化为TXT格式，并将转化后的TXT数据保存为source.txt和target.txt文件，保存在/data文件夹。源数据文件格式如下：
​​	文本字段（text）：存储原始医疗文本描述，内容包含疾病分类、病因、关联疾病等医学知识。
​​	三元组列表（triple_list）：以列表形式存储结构化三元组，每个三元组为 [subject, predicate, object] 结构：
​​Subject：医疗实体（如疾病、药物、症状）。
​​Predicate：实体间关系类型（如“病理分型”“病因”）。
​​Object：与主语关联的实体或属性值。
目标数据文件格式如下，示例请参照source_sample.txt和target_sample.txt文件：
source_sample.txt：存储原始文本内容，包含待提取的语义关系信息。
target_sample.txt：以列表形式存储结构化三元组。
source_sample.txt和target_sample.txt两个文件一一对应。
（1）步骤1.转换文件格式（1分）
部分代码已给出，请根据提示，将代码补充完整。
定义jsonl_to_txt函数，该函数的任务是根据要求将jsonl文件转换为txt格式，并符合数据的格式要求。运行该文件将转化后的数据保存为source.txt和target.txt文件，保存在/data文件夹。将<1>处补充的代码截图并保存在“answer.doc”中。
2.知识图谱构建（base/use_llm.py）（7分）
（1）步骤1.配置大模型（1分）
部分代码已给出，请根据提示，将代码补充完整。
按提示加载大语言模型。将<1>处补充的代码截图并保存在“answer.doc”中。
（2）步骤2.构造few shot示例（2分）
部分代码已给出，请根据提示，将代码补充完整。
在extract_triples函数中完善few shot示例，帮助大模型理解具体任务。部分代码已给出，请根据提示，将代码补充完整。将<2>处补充的代码截图并保存在“answer.doc”中。
提示：可选取CMeIE-V2_train_triples.jsonl中的例子作为few shot示例。
（3）步骤3.构造prompt提示词（3分）
部分代码已给出，请根据提示，将代码补充完整。
构造三元组提取prompt提示词,并将few shot示例组合进提示词中，将<3>处补充的代码截图并保存在“answer.doc”中。
（4）步骤4.大模型调用（1分）
部分代码已给出，请根据提示，将代码补充完整。
调用大模型根据model_inputs实现三元组提取，这里限制模型最多生成512个新token（max_new_tokens），temperature设置为0.1。将<4>处补充的代码截图并保存在“answer.doc”中。
（5）步骤5.执行三元组提取（1分）
部分代码已给出，请根据提示，将代码补充完整。
执行大模型根据输入文本进行三元提取，并将结果保存到指定的文件中。将<5>处补充的代码截图并保存在“answer.doc”中。
执行use_llm.py，并将提取的三元组保存到result_kg.txt文件。
3.知识图谱精度评测（base/evaluation_script.py）（2分）
（1）步骤1.模型性能指标定义（1分，3个题全部答对给1分）
部分代码已给出，请根据提示，将代码补充完整。
简写	全称	含义
TP	True Positive	预测的三元组与真实三元组匹配
FP	False Positive	预测的三元组与真实三元组不匹配
FN	False Negative	未预测到的真实三元组
①计算精确率。注意需要检查真实标签数量是否为0，避免除以零。将<1>处补充的代码截图并保存在“answer.doc”中。
②计算召回率分数。注意需要检查真实标签数量是否为0，避免除以零。将<2>处补充的代码截图并保存在“answer.doc”中。
③计算模型的F1分数。注意只有当精确率和召回率都为0时，F1才为0。将<3>处补充的代码截图并保存在“answer.doc”中。
（2）步骤2. 衡量提取精度（1分）
部分代码已给出，请根据提示，将代码补充完整。
补全大模型提取三元组和真实三元组之间的精度计算，将<4>处补充的代码截图并保存在“answer.doc”中。
执行evaluation_script.py的main函数，得到结果答案，将运行结果保存在“main_result.txt”。
注：编写完成后，需要将“./data”中的“result_kg.txt”文件，和evaluation_script.py文件运行结果“main_result.txt”保存到“res”文件夹中。