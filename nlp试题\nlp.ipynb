import os
import json
from typing import List, Dict, Any, <PERSON><PERSON>

def split_sentences(text: str) -> List[str]:
    """
    根据特定标点符号分割文本为句子列表
    
    Args:
        text: 待分割的文本
    
    Returns:
        分割后的句子列表
    """
    sentence_endings = "。！？"
    sentences = []
    start = 0
    #---------------------------------------1------------------------------------------
    # TODO: 实现文本分割逻辑
    # 遍历文本字符，当遇到句子结束符时，将从start到当前位置的文本作为一个句子
    # 需要：1. 添加句子到sentences列表 2. 更新start位置 3. 处理剩余文本
    for i in range(len(text)):
        if text[i] in sentence_endings:
            sentences._____
            start = _____
    if start < len(text):
        sentences._____
    #---------------------------------------1------------------------------------------
    return sentences

def process_entity(
    sentence: str, 
    sent_text: str, 
    entity: Dict[str, Any],
    tags: List[str]
) -> None:
    """
    处理单个实体，生成对应的BIOES标签
    
    Args:
        sentence: 当前处理的句子
        sent_text: 包含实体的完整文本
        entity: 实体信息字典
        tags: 标签列表，将被修改
        B: 实体的开始
        E: 实体的结束
        I: 实体的内部
        S: 单字符实体
        O: 非实体
    """
    start_idx = entity.get('start_idx', 0) - sent_text.index(sentence)
    end_idx = entity.get('end_idx', 0) - sent_text.index(sentence)
    entity_type = entity.get('entity_type', '')
    
    # 检查实体索引是否在当前句子范围内
    if start_idx < 0 or end_idx > len(sentence):
        return
    #------------------------------------2------------------------------
    # TODO: 根据实体长度设置BIOES标签
    # 单字实体使用S标签，多字实体使用B-I-E标签组合
    # 标签格式：标签类型-实体类型，如'S-PER'、'B-ORG'等
    if start_idx == end_idx - 1:
        # 单字实体
        tags[start_idx] = _____
    else:
        # 多字实体
        tags[start_idx] = _____
        for i in range(start_idx + 1, end_idx - 1):
            tags[i] = _____
        tags[end_idx - 1] = _____
    #------------------------------------2------------------------------

def convert_to_bioes(json_data: Dict[str, Any]) -> List[str]:
    """
    将JSON格式的数据转换为BIOES标注格式
    
    Args:
        json_data: 包含段落和实体信息的JSON数据
    
    Returns:
        BIOES格式的行列表
    """
    bioes_lines = []
    
    for paragraph in json_data.get('paragraphs', []):
        paragraph_text = paragraph.get('paragraph', '')
        sentences = split_sentences(paragraph_text)
        
        for sentence in sentences:
            # 初始化标签列表
            tags = ['O'] * len(sentence)
            #------------------------------------3------------------------------
            # TODO: 遍历段落中的句子信息，处理实体标注
            # 需要：1. 获取句子信息 2. 匹配当前句子 3. 处理句子中的实体
            for sent_info in paragraph.get('sentences', []):
                sent_text = sent_info.get('sentence', '')
                if sentence in sent_text:
                    entities = sent_info.get('entities', [])
                    for entity in entities:
                        _____
            #------------------------------------3------------------------------
            # 生成BIOES格式的行，忽略空格、回车和制表符
            for char, tag in zip(sentence, tags):
                if char.strip():
                    bioes_lines.append(f'{char} {tag}')
            bioes_lines.append('')  # 句子之间用空行隔开
            
    
    return bioes_lines

def process_directory(input_dir: str) -> List[str]:
    """
    处理目录中的所有JSON文件，转换为BIOES格式
    
    Args:
        input_dir: 输入目录路径
    
    Returns:
        所有文件的BIOES格式行列表
    """
    all_bioes_lines = []
    
    # 遍历目录中的所有JSON文件
    for filename in os.listdir(input_dir):
        if filename.endswith('.json'):
            file_path = os.path.join(input_dir, filename)
            try:#----------------------------------------------4--------------------------------------
                # TODO: 处理JSON文件并转换为BIOES格式
                # 需要：1. 加载JSON数据 2. 转换为BIOES格式 3. 添加到总列表
                with open(file_path, 'r', encoding='utf-8') as f:
                    json_data = json._____
                    bioes_lines = _____
                    all_bioes_lines._____
                #----------------------------------------------4--------------------------------------
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
    
    return all_bioes_lines

from os.path import join
from codecs import open


def build_map(lists):
    maps = {}
    for list_ in lists:
        for e in list_:
            if e not in maps:
                maps[e] = len(maps)
    return maps

def build_corpus(split, make_vocab=True, data_dir="./data"):
    """读取数据"""
    assert split in ['train', 'dev', 'test']

    word_lists = []
    tag_lists = []
    with open(join(data_dir, split+".bioes"), 'r', encoding='utf-8') as f:
        word_list = []
        tag_list = []
        #---------------------------------------1----------------------------------------
        # TODO: 读取BIOES格式文件并解析词和标签
        # 需要：1. 分割每行的词和标签 2. 添加到列表 3. 处理句子边界
        for line in f:
            if line != '\n':
                word, tag = _____
                word_list.append(word)
                tag_list.append(tag)
            elif (word_list!= []) and (tag_list!= [] ):
                word_lists._____
                tag_lists._____
                word_list = []
                tag_list = []
        #---------------------------------------1----------------------------------------

    #------------------------------------------2-----------------------------------
    # TODO: 构建词汇表和标签映射
    # 需要：1. 构建word2id映射 2. 构建tag2id映射
    # 如果make_vocab为True，还需要返回word2id和tag2id
    if make_vocab:
        word2id = _____
        tag2id = _____
        return word_lists, tag_lists, word2id, tag2id
    else:
        return word_lists, tag_lists
    #------------------------------------------2-----------------------------------

from sklearn_crfsuite import CRF
from .util import sent2features

class CRFModel(object):
    def __init__(self,
                 algorithm='lbfgs',
                 c1=0.1,
                 c2=0.1,
                 max_iterations=100,
                 all_possible_transitions=False
                 ):

        self.model = CRF(algorithm=algorithm,
                         c1=c1,
                         c2=c2,
                         max_iterations=max_iterations,
                         all_possible_transitions=all_possible_transitions)

    def train(self, sentences, tag_lists):
        #-----------------------------------------------1--------------------------------------------
        # TODO: 训练CRF模型
        # 需要：1. 提取句子特征 2. 训练模型
        features = _____
        self.model._____
        #-----------------------------------------------1--------------------------------------------

    def test(self, sentences):
        #-----------------------------------------------2--------------------------------------------
        # TODO: 测试CRF模型
        # 需要：1. 提取句子特征 2. 预测标签
        features = _____
        pred_tag_lists = self.model._____
        #-----------------------------------------------2--------------------------------------------
        return pred_tag_lists

import torch
import torch.nn as nn
from torch.nn.utils.rnn import pad_packed_sequence, pack_padded_sequence


class BiLSTM(nn.Module):
    def __init__(self, vocab_size, emb_size, hidden_size, out_size):
        """初始化参数：
            vocab_size:字典的大小
            emb_size:词向量的维数
            hidden_size：隐向量的维数
            out_size:标注的种类
        """
        super(BiLSTM, self).__init__()
        #-----------------------------------------------1--------------------------------------------
        # TODO: 定义词向量层
        # 需要：词典大小、词向量维度
        self.embedding = _____
        #-----------------------------------------------1--------------------------------------------
        #-----------------------------------------------2--------------------------------------------
        # TODO: 定义BiLSTM层
        # 需要：输入维度、隐藏层维度、batch_first=True、bidirectional=True
        self.bilstm = nn.LSTM(
            _____
            )
        #-----------------------------------------------2--------------------------------------------
        #-----------------------------------------------3--------------------------------------------
        # TODO: 定义输出全连接层
        # 需要：输入维度（BiLSTM隐藏层维度*2）、输出维度（标签数量）
        self.lin = nn.Linear(
            _____
        )
        #-----------------------------------------------3--------------------------------------------

    def forward(self, sents_tensor, lengths):
        #-----------------------------------------------4--------------------------------------------
        # TODO: 实现BiLSTM前向传播
        # 需要：1. 词向量化 2. BiLSTM编码 3. 全连接层输出
        emb = _____

        packed = pack_padded_sequence(emb, lengths, batch_first=True)
        rnn_out, _ = _____
        rnn_out, _ = pad_packed_sequence(rnn_out, batch_first=True)

        scores = _____
        #-----------------------------------------------4--------------------------------------------
        return scores

    def test(self, sents_tensor, lengths, _):
        """第三个参数不会用到，加它是为了与BiLSTM_CRF保持同样的接口"""
        logits = self.forward(sents_tensor, lengths)  # [B, L, out_size]
        _, batch_tagids = torch.max(logits, dim=2)

        return batch_tagids

import torch


class HMM(object):
    def __init__(self, N, M):
        """Args:
            N: 状态数，这里对应存在的标注的种类
            M: 观测数，这里对应有多少不同的字
        """
        self.N = N
        self.M = M

        # 状态转移概率矩阵 A[i][j]表示从i状态转移到j状态的概率
        self.A = torch.zeros(N, N)
        # 观测概率矩阵, B[i][j]表示i状态下生成j观测的概率
        self.B = torch.zeros(N, M)
        # 初始状态概率  Pi[i]表示初始时刻为状态i的概率
        self.Pi = torch.zeros(N)

    def train(self, word_lists, tag_lists, word2id, tag2id):
        """HMM的训练，即根据训练语料对模型参数进行估计,
           因为我们有观测序列以及其对应的状态序列，所以我们
           可以使用极大似然估计的方法来估计隐马尔可夫模型的参数
        参数:
            word_lists: 列表，其中每个元素由字组成的列表，如 ['担','任','科','员']
            tag_lists: 列表，其中每个元素是由对应的标注组成的列表，如 ['O','O','B-TITLE', 'E-TITLE']
            word2id: 将字映射为ID
            tag2id: 字典，将标注映射为ID
        """

        assert len(tag_lists) == len(word_lists)

        # 估计转移概率矩阵
        #-----------------------------------------------1--------------------------------------------
        # TODO: 估计状态转移概率矩阵
        # 需要：1. 获取当前和下一个标签ID 2. 更新转移计数 3. 归一化
        for tag_list in tag_lists:
            seq_len = len(tag_list)
            for i in range(seq_len - 1):
                current_tagid = _____
                next_tagid = _____
                self.A[current_tagid][next_tagid] += _____
        # 问题：如果某元素没有出现过，该位置为0，这在后续的计算中是不允许的
        # 解决方法：我们将等于0的概率加上很小的数
        self.A[self.A == 0.] = 1e-10
        # 归一化
        self.A = _____
        #-----------------------------------------------1--------------------------------------------

        # 估计观测概率矩阵
        #-----------------------------------------------2--------------------------------------------
        # TODO: 估计观测概率矩阵
        # 需要：1. 获取标签和词的ID 2. 更新观测计数 3. 归一化
        for tag_list, word_list in zip(tag_lists, word_lists):
            assert len(tag_list) == len(word_list)
            for tag, word in zip(tag_list, word_list):
                tag_id = _____
                word_id = _____
                self.B[tag_id][word_id] += _____
        self.B[self.B == 0.] = 1e-10
        # 归一化
        self.B = _____
        #-----------------------------------------------2--------------------------------------------

        # 估计初始状态概率
        #-----------------------------------------------3--------------------------------------------
        # TODO: 估计初始状态概率分布
        # 需要：1. 获取序列第一个标签ID 2. 更新初始状态计数 3. 归一化
        for tag_list in tag_lists:
            init_tagid = _____
            self.Pi[init_tagid] += _____
        self.Pi[self.Pi == 0.] = 1e-10
        # 归一化
        self.Pi = _____
        #-----------------------------------------------3--------------------------------------------

    def test(self, word_lists, word2id, tag2id):
        pred_tag_lists = []
        for word_list in word_lists:
            pred_tag_list = self.decoding(word_list, word2id, tag2id)
            pred_tag_lists.append(pred_tag_list)
        return pred_tag_lists

    def decoding(self, word_list, word2id, tag2id):
        """
        使用维特比算法对给定观测序列求状态序列， 这里就是对字组成的序列,求其对应的标注。
        维特比算法实际是用动态规划解隐马尔可夫模型预测问题，即用动态规划求概率最大路径（最优路径）
        这时一条路径对应着一个状态序列
        """
        # 问题:整条链很长的情况下，十分多的小概率相乘，最后可能造成下溢
        # 解决办法：采用对数概率，这样源空间中的很小概率，就被映射到对数空间的大的负数
        #  同时相乘操作也变成简单的相加操作
        A = torch.log(self.A)
        B = torch.log(self.B)
        Pi = torch.log(self.Pi)

        # 初始化 维比特矩阵viterbi 它的维度为[状态数, 序列长度]
        # 其中viterbi[i, j]表示标注序列的第j个标注为i的所有单个序列(i_1, i_2, ..i_j)出现的概率最大值
        seq_len = len(word_list)
        viterbi = torch.zeros(self.N, seq_len)
        # backpointer是跟viterbi一样大小的矩阵
        # backpointer[i, j]存储的是 标注序列的第j个标注为i时，第j-1个标注的id
        # 等解码的时候，我们用backpointer进行回溯，以求出最优路径
        backpointer = torch.zeros(self.N, seq_len).long()

        # self.Pi[i] 表示第一个字的标记为i的概率
        # Bt[word_id]表示字为word_id的时候，对应各个标记的概率
        # self.A.t()[tag_id]表示各个状态转移到tag_id对应的概率

        # 所以第一步为
        start_wordid = word2id.get(word_list[0], None)
        Bt = B.t()
        if start_wordid is None:
            # 如果字不在字典里，则假设状态的概率分布是均匀的
            bt = torch.log(torch.ones(self.N) / self.N)
        else:
            bt = Bt[start_wordid]
        viterbi[:, 0] = Pi + bt
        backpointer[:, 0] = -1

        # 递推公式：
        # viterbi[tag_id, step] = max(viterbi[:, step-1]* self.A.t()[tag_id] * Bt[word])
        # 其中word是step时刻对应的字
        # 由上述递推公式求后续各步
        for step in range(1, seq_len):
            wordid = word2id.get(word_list[step], None)
            # 处理字不在字典中的情况
            # bt是在t时刻字为wordid时，状态的概率分布
            if wordid is None:
                # 如果字不在字典里，则假设状态的概率分布是均匀的
                bt = torch.log(torch.ones(self.N) / self.N)
            else:
                bt = Bt[wordid]  # 否则从观测概率矩阵中取bt
            for tag_id in range(len(tag2id)):
                #-----------------------------------------------4--------------------------------------------
                # TODO: 实现维特比算法递推公式
                # 需要：1. 计算前一步概率+转移概率+观测概率 2. 找到最大值和对应状态
                max_prob, max_id = torch.max(
                    _____
                )                
                viterbi[tag_id, step] = _____
                #-----------------------------------------------4--------------------------------------------
                backpointer[tag_id, step] = max_id

        # 终止， t=seq_len 即 viterbi[:, seq_len]中的最大概率，就是最优路径的概率
        #-----------------------------------------------5--------------------------------------------
        # TODO: 找到最优路径的终点状态
        # 需要：从最后一个时间步的所有状态中找到概率最大的状态
        best_path_prob, best_path_pointer = torch.max(
            _____
        )
        #-----------------------------------------------5--------------------------------------------

        # 回溯，求最优路径
        best_path_pointer = best_path_pointer.item()
        best_path = [best_path_pointer]
        #-----------------------------------------------6--------------------------------------------
        # TODO: 实现路径回溯
        # 需要：从终点开始，利用backpointer逐步回溯到起点
        for back_step in range(seq_len-1, 0, -1):
            best_path_pointer = _____
            best_path_pointer = best_path_pointer.item()
            best_path.append(best_path_pointer)
        #-----------------------------------------------6--------------------------------------------
        
        # 将tag_id组成的序列转化为tag
        assert len(best_path) == len(word_list)
        id2tag = dict((id_, tag) for tag, id_ in tag2id.items())
        tag_list = [id2tag[id_] for id_ in reversed(best_path)]

        return tag_list

from itertools import zip_longest
from copy import deepcopy

import torch
import torch.nn as nn
import torch.optim as optim

from .util import tensorized, sort_by_lengths, cal_loss, cal_lstm_crf_loss
from .config import TrainingConfig, LSTMConfig
from .bilstm import BiLSTM


class BiLSTM_CRF(nn.Module):
    def __init__(self, vocab_size, emb_size, hidden_size, out_size):
        """初始化参数：
            vocab_size:字典的大小
            emb_size:词向量的维数
            hidden_size：隐向量的维数
            out_size:标注的种类
        """
        super(BiLSTM_CRF, self).__init__()
        self.bilstm = BiLSTM(vocab_size, emb_size, hidden_size, out_size)

        # CRF实际上就是多学习一个转移矩阵 [out_size, out_size] 并进行均匀初始化
        #------------------------------------------------------1---------------------------------------------
        # TODO: 定义CRF转移矩阵
        # 需要：创建可训练的转移矩阵参数，大小为(out_size, out_size)
        self.transition = nn.Parameter(
            _____
            )
        #------------------------------------------------------1---------------------------------------------
        # self.transition.data.zero_()

    def forward(self, sents_tensor, lengths):
        # [B, L, out_size]
        #------------------------------------------------------2---------------------------------------------
        # TODO: 计算BiLSTM-CRF的前向传播
        # 需要：1. 获取BiLSTM发射分数 2. 计算CRF分数矩阵
        emission = _____
        
        # 计算CRF scores, 这个scores大小为[B, L, out_size, out_size]
        # 也就是每个字对应对应一个 [out_size, out_size]的矩阵
        # 这个矩阵第i行第j列的元素的含义是：上一时刻tag为i，这一时刻tag为j的分数
        batch_size, max_len, out_size = emission.size()
        crf_scores = _____
        #------------------------------------------------------2---------------------------------------------
        return crf_scores

    def test(self, test_sents_tensor, lengths, tag2id):
        """使用维特比算法进行解码"""
        start_id = tag2id['<start>']
        end_id = tag2id['<end>']
        pad = tag2id['<pad>']
        tagset_size = len(tag2id)

        crf_scores = self.forward(test_sents_tensor, lengths)
        device = crf_scores.device
        # B:batch_size, L:max_len, T:target set size
        B, L, T, _ = crf_scores.size()
        # viterbi[i, j, k]表示第i个句子，第j个字对应第k个标记的最大分数
        viterbi = torch.zeros(B, L, T).to(device)
        # backpointer[i, j, k]表示第i个句子，第j个字对应第k个标记时前一个标记的id，用于回溯
        backpointer = (torch.zeros(B, L, T).long() * end_id).to(device)
        lengths = torch.LongTensor(lengths).to(device)
        # 向前递推
        for step in range(L):
            batch_size_t = (lengths > step).sum().item()
            
            if step == 0:
                # 第一个字它的前一个标记只能是start_id
                viterbi[:batch_size_t, step,:] = crf_scores[: batch_size_t, step, start_id, :]
                backpointer[: batch_size_t, step, :] = start_id
            else:
            #------------------------------------------------------3---------------------------------------------
                # TODO: 实现BiLSTM-CRF的维特比解码前向递推
                # 需要：计算前一步概率加上当前步转移分数，找到最大值和对应的前驱标签
                max_scores, prev_tags = torch.max(
                    _____
                )
                viterbi[:batch_size_t, step, :] = max_scores
                backpointer[:batch_size_t, step, :] = prev_tags
            #------------------------------------------------------3---------------------------------------------
        # 在回溯的时候我们只需要用到backpointer矩阵
        backpointer = backpointer.view(B, -1)  # [B, L * T]
        tagids = []  # 存放结果
        tags_t = None
        for step in range(L-1, 0, -1):
            batch_size_t = (lengths > step).sum().item()
            if step == L-1:
                index = torch.ones(batch_size_t).long() * (step * tagset_size)
                index = index.to(device)
                index += end_id
            else:
                prev_batch_size_t = len(tags_t)

                new_in_batch = torch.LongTensor(
                    [end_id] * (batch_size_t - prev_batch_size_t)).to(device)
                offset = torch.cat(
                    [tags_t, new_in_batch],
                    dim=0
                )  # 这个offset实际上就是前一时刻的
                index = torch.ones(batch_size_t).long() * (step * tagset_size)
                index = index.to(device)
                index += offset.long()

            try:
                tags_t = backpointer[:batch_size_t].gather(
                    dim=1, index=index.unsqueeze(1).long())
            except RuntimeError:
                import pdb
                pdb.set_trace()
            tags_t = tags_t.squeeze(1)
            tagids.append(tags_t.tolist())

        # tagids:[L-1]（L-1是因为扣去了end_token),大小的liebiao
        # 其中列表内的元素是该batch在该时刻的标记
        # 下面修正其顺序，并将维度转换为 [B, L]
        tagids = list(zip_longest(*reversed(tagids), fillvalue=pad))
        tagids = torch.Tensor(tagids).long()

        # 返回解码的结果
        return tagids



class BILSTM_Model(object):
    def __init__(self, vocab_size, out_size, crf=True):
        """功能：对LSTM的模型进行训练与测试
           参数:
            vocab_size:词典大小
            out_size:标注种类
            crf选择是否添加CRF层"""
        self.device = torch.device(
            "cuda" if torch.cuda.is_available() else "cpu")

        # 加载模型参数
        self.emb_size = LSTMConfig.emb_size
        self.hidden_size = LSTMConfig.hidden_size

        self.crf = crf
        # 根据是否添加crf初始化不同的模型 选择不一样的损失计算函数
        if not crf:
            self.model = BiLSTM(vocab_size, self.emb_size,
                                self.hidden_size, out_size).to(self.device)
            self.cal_loss_func = cal_loss
        else:
            #------------------------------------------------------4---------------------------------------------
            # TODO: 创建BiLSTM-CRF模型实例
            # 需要：传入词典大小、词向量维度、隐藏层维度、输出维度参数
            self.model = BiLSTM_CRF(
                _____
            ).to(self.device)
            #------------------------------------------------------4---------------------------------------------
            self.cal_loss_func = cal_lstm_crf_loss

        # 加载训练参数：
        self.epoches = TrainingConfig.epoches
        self.print_step = TrainingConfig.print_step
        self.lr = TrainingConfig.lr
        self.batch_size = TrainingConfig.batch_size

        # 初始化优化器
        #------------------------------------------------------5---------------------------------------------
        # TODO: 定义优化器
        # 需要：使用Adam优化器，传入模型参数和学习率
        self.optimizer = _____
        #------------------------------------------------------5---------------------------------------------

        # 初始化其他指标
        self.step = 0
        self._best_val_loss = 1e18
        self.best_model = None

    def train(self, word_lists, tag_lists,
              dev_word_lists, dev_tag_lists,
              word2id, tag2id):
        # 对数据集按照长度进行排序
        word_lists, tag_lists, _ = sort_by_lengths(word_lists, tag_lists)
        dev_word_lists, dev_tag_lists, _ = sort_by_lengths(
            dev_word_lists, dev_tag_lists)

        B = self.batch_size
        for e in range(1, self.epoches+1):
            self.step = 0
            losses = 0.
            for ind in range(0, len(word_lists), B):
                batch_sents = word_lists[ind:ind+B]
                batch_tags = tag_lists[ind:ind+B]

                losses += self.train_step(batch_sents,
                                          batch_tags, word2id, tag2id)

                if self.step % TrainingConfig.print_step == 0:
                    total_step = (len(word_lists) // B + 1)
                    print("Epoch {}, step/total_step: {}/{} {:.2f}% Loss:{:.4f}".format(
                        e, self.step, total_step,
                        100. * self.step / total_step,
                        losses / self.print_step
                    ))
                    losses = 0.

            # 每轮结束测试在验证集上的性能，保存最好的一个
            val_loss = self.validate(
                dev_word_lists, dev_tag_lists, word2id, tag2id)
            print("Epoch {}, Val Loss:{:.4f}".format(e, val_loss))

    def train_step(self, batch_sents, batch_tags, word2id, tag2id):
        self.model.train()
        self.step += 1
        # 准备数据
        tensorized_sents, lengths = tensorized(batch_sents, word2id)
        tensorized_sents = tensorized_sents.to(self.device)
        targets, lengths = tensorized(batch_tags, tag2id)
        targets = targets.to(self.device)

        # forward
        #------------------------------------------------------6---------------------------------------------
        # TODO: 完成模型前向传播和损失计算
        # 需要：1. 模型前向传播获得分数 2. 计算损失
        scores = _____

        # 计算损失 更新参数
        self.optimizer.zero_grad()
        loss = _____
        loss.backward()
        self.optimizer.step()
        #------------------------------------------------------6---------------------------------------------

        return loss.item()

from collections import Counter

from utils import flatten_lists


class Metrics(object):
    """用于评价模型，计算每个标签的精确率，召回率，F1分数"""

    def __init__(self, golden_tags, predict_tags, remove_O=False):

        # [[t1, t2], [t3, t4]...] --> [t1, t2, t3, t4...]
        self.golden_tags = flatten_lists(golden_tags)
        self.predict_tags = flatten_lists(predict_tags)

        if remove_O:  # 将O标记移除，只关心实体标记
            self._remove_Otags()

        # 辅助计算的变量
        self.tagset = set(self.golden_tags)
        self.correct_tags_number = self.count_correct_tags()
        self.predict_tags_counter = Counter(self.predict_tags)
        self.golden_tags_counter = Counter(self.golden_tags)

        # 计算精确率
        self.precision_scores = self.cal_precision()

        # 计算召回率
        self.recall_scores = self.cal_recall()

        # 计算F1分数
        self.f1_scores = self.cal_f1()

    def cal_precision(self):
        """计算精确率"""
        precision_scores = {}
        #------------------------------------------------------1---------------------------------------------
        # TODO: 计算精确率
        # 需要：正确预测的标签数量 / 预测的标签总数量
        for tag in self.tagset:
            # 检查预测标签数量是否为0，避免除以零
            if self.predict_tags_counter[tag] == 0:
                precision_scores[tag] = 0.0
            else:
                precision_scores[tag] = _____
        #------------------------------------------------------1---------------------------------------------
        return precision_scores

    def cal_recall(self):
        """计算召回率"""
        recall_scores = {}
        #------------------------------------------------------2---------------------------------------------
        # TODO: 计算召回率
        # 需要：正确预测的标签数量 / 真实标签总数量
        for tag in self.tagset:
            # 检查真实标签数量是否为0，避免除以零
            if self.golden_tags_counter[tag] == 0:
                recall_scores[tag] = 0.0
            else:
                recall_scores[tag] = _____
        #------------------------------------------------------2---------------------------------------------            
        return recall_scores

    def cal_f1(self):
        """计算F1分数"""
        f1_scores = {}
        #------------------------------------------------------3---------------------------------------------
        # TODO: 计算F1分数
        # 需要：2 * 精确率 * 召回率 / (精确率 + 召回率)
        for tag in self.tagset:
            p, r = self.precision_scores[tag], self.recall_scores[tag]
            # 只有当精确率和召回率都为0时，F1才为0
            if p == 0 and r == 0:
                f1_scores[tag] = 0.0
            else:
                f1_scores[tag] = _____
        #------------------------------------------------------3---------------------------------------------
        return f1_scores

import time
from collections import Counter

from models.hmm import HMM
from models.crf import CRFModel
from models.bilstm_crf import BILSTM_Model
from utils import save_model, flatten_lists
from evaluating import Metrics


def crf_train_eval(train_data, test_data, remove_O=False):

    # 训练CRF模型
    train_word_lists, train_tag_lists = train_data
    test_word_lists, test_tag_lists = test_data
    
    #------------------------------------------------------1---------------------------------------------
    # TODO: CRF模型训练、保存和测试
    # 需要：1. 训练模型 2. 保存模型 3. 测试模型
    crf_model = CRFModel()
    crf_model._____
    save_model(
        _____
    )
    
    pred_tag_lists = crf_model._____

    metrics = Metrics(test_tag_lists, pred_tag_lists, remove_O=remove_O)
    #------------------------------------------------------1---------------------------------------------
    
    metrics.report_scores()
    metrics.report_confusion_matrix()

    return pred_tag_lists


def hmm_train_eval(train_data, test_data, word2id, tag2id, remove_O=False):
    """训练并评估hmm模型"""
    # 训练HMM模型
    train_word_lists, train_tag_lists = train_data
    test_word_lists, test_tag_lists = test_data

    #------------------------------------------------------2---------------------------------------------
    # TODO: HMM模型训练、保存和测试
    # 需要：1. 训练模型 2. 保存模型 3. 测试模型
    hmm_model = HMM(len(tag2id), len(word2id))
    hmm_model._____
    save_model(
        _____
    )

    # 评估hmm模型
    pred_tag_lists = hmm_model._____

    metrics = Metrics(test_tag_lists, pred_tag_lists, remove_O=remove_O)
    #------------------------------------------------------2---------------------------------------------
        
    metrics.report_scores()
    metrics.report_confusion_matrix()

    return pred_tag_lists


def bilstm_train_and_eval(train_data, dev_data, test_data,
                          word2id, tag2id, crf=True, remove_O=False):
    train_word_lists, train_tag_lists = train_data
    dev_word_lists, dev_tag_lists = dev_data
    test_word_lists, test_tag_lists = test_data

    start = time.time()
    vocab_size = len(word2id)
    out_size = len(tag2id)
    #------------------------------------------------------3---------------------------------------------
    bilstm_model = BILSTM_Model(vocab_size, out_size, crf=crf)
    bilstm_model.

    model_name = "bilstm_crf" if crf else "bilstm"
    save_model(
        
    )

    print("训练完毕,共用时{}秒.".format(int(time.time()-start)))
    print("评估{}模型中...".format(model_name))
    # TODO: BiLSTM模型测试
    # 需要：调用模型的测试方法
    pred_tag_lists, test_tag_lists = bilstm_model._____

    metrics = Metrics(test_tag_lists, pred_tag_lists, remove_O=remove_O)
    #------------------------------------------------------3---------------------------------------------
    
    metrics.report_scores()
    metrics.report_confusion_matrix()

    return pred_tag_lists

import json
import os


# 1.数据处理
def jsonl_to_txt(jsonl_file_path, text_output_path, triple_output_path):
    """
    将JSONL文件转换为两个TXT文件：
    - text_output_path: 每行存储"text"字段内容
    - triple_output_path: 每行存储"triple_list"的JSON字符串
    """
    try:
        with open(jsonl_file_path, 'r', encoding='utf-8') as jsonl_file, \
                open(text_output_path, 'w', encoding='utf-8') as text_file, \
                open(triple_output_path, 'w', encoding='utf-8') as triple_file:

            line_count = 0
            for line in jsonl_file:
                #------------------------------------------------------1---------------------------------------------
                # TODO: 解析JSONL文件并提取字段
                # 需要：1. 解析JSON数据 2. 提取text字段 3. 提取triple_list字段
                # 解析JSONL每行数据
                data = json._____

                # 提取text字段并写入文本文件
                text_content = data._____
                text_file.write(text_content + "\n")

                # 提取triple_list字段并序列化为JSON字符串
                triple_list = data._____
                triple_file.write(json.dumps(triple_list, ensure_ascii=False) + "\n")
                #------------------------------------------------------1---------------------------------------------
                line_count += 1
                if line_count % 1000 == 0:  # 每处理1000行打印进度
                    print(f"已处理 {line_count} 行...")

            print(f"转换完成！共处理 {line_count} 行数据")
            print(f"文本文件保存至: {os.path.abspath(text_output_path)}")
            print(f"三元组文件保存至: {os.path.abspath(triple_output_path)}")

    except FileNotFoundError:
        print(f"错误: 文件 {jsonl_file_path} 不存在")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"未知错误: {e}")

# 示例用法
if __name__ == "__main__":
    jsonl_to_txt(
        jsonl_file_path="CMeIE-V2_test_triples.jsonl",  # 输入的JSONL文件路径
        text_output_path="source.txt",  # 输出的文本文件路径
        triple_output_path="target.txt"  # 输出的三元组文件路径
    )

from modelscope import AutoModelForCausalLM, AutoTokenizer
import torch
import ast
import os
from tqdm import tqdm

class QwenModel:
    def __init__(self):
        """
        初始化Qwen模型
        参数：
            device: 计算设备 (cuda/cpu)
            torch_dtype: 张量精度
            model_path: 模型所在路径(默认Qwen3-0.6B,可修改为Qwen3-4B)
        """
        self.device = "cuda"
        self.torch_dtype = torch.bfloat16
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.model_path = os.path.abspath(os.path.join(script_dir, "../../Qwen3-0.6B"))

        # 延迟加载组件
        self._model = None
        self._tokenizer = None

    @property
    def tokenizer(self):
        if self._tokenizer is None:
            self._tokenizer = AutoTokenizer.from_pretrained(self.model_path)
        return self._tokenizer
    
    @property
    def model(self):
        if self._model is None:
            #------------------------------------------------------1---------------------------------------------
            # TODO: 配置大模型加载参数
            # 需要：模型路径、设备类型、数据类型等参数
            self._model = AutoModelForCausalLM.from_pretrained(
                _____
            ).to(self.torch_dtype).to(self.device).eval()
            #------------------------------------------------------1---------------------------------------------
        return self._model

def extract_triples(self, user_input):
    try:
        # 构造few shot示例
        #------------------------------------------------------2---------------------------------------------
        # TODO: 构造few shot示例
        # 需要：选择合适的示例数据，构造输入输出对
        few_shot = {
            _____
        }
        #------------------------------------------------------2---------------------------------------------

# 构造prompt
#------------------------------------------------------3---------------------------------------------
# TODO: 构造prompt提示词
# 需要：1. 设计系统角色和任务描述 2. 整合few shot示例 3. 构造用户输入格式
messages = [
    {
        _____
    }
]
#------------------------------------------------------3---------------------------------------------
# 测试三元组提取示例
messages.append({"role": "user", "content": "user input text:" + user_input})

# 生成模型输入
text = self.tokenizer.apply_chat_template(
    messages,
    tokenize=False,
    add_generation_prompt=True,
    enable_thinking=False
)
            
# 生成参数设置
model_inputs = self.tokenizer([text], return_tensors="pt").to(self.model.device)

# 三元组提取
#------------------------------------------------------4---------------------------------------------
# TODO: 设置模型推理参数
# 需要：设置max_new_tokens、temperature等参数
generated_ids = self.model.generate(
    _____
)
#------------------------------------------------------4---------------------------------------------

if __name__ == "__main__":

    text_path = os.path.join('..', 'data', 'source.txt')
    kg_path = os.path.join('..', 'data', 'target.txt')
    result_path = os.path.join('..', 'data', 'result_kg.txt')

    with open(text_path, 'r', encoding='utf-8') as file:
        text = [line.strip() for line in file]
    
    with open(kg_path, 'r', encoding='utf-8') as file:
        kg = [ast.literal_eval(line.strip()) for line in file]

    print(f"webnlg.txt文本个数为：{len(text)}, webnlg_kg.txt三元组个数为：{len(kg)}")

    model = QwenModel()
    # 打开 result_kg.txt 文件用于写入
    with open(result_path, 'w', encoding='utf-8') as result_file:
        # 知识图谱构建
        for i in tqdm(range(len(text)), desc="调用qwen3提取三元组:"):
            #------------------------------------------------------5---------------------------------------------
            # TODO: 调用模型提取三元组
            # 需要：调用模型的三元组提取方法
            triples = model._____
            result_file.write(triples + '\n')  # 写入文件并换行
            #------------------------------------------------------5---------------------------------------------

import ast
import os

def calculate_precision(tp, fp):
    """计算精确率"""
    #------------------------------------------------------1---------------------------------------------
    return _____
    #------------------------------------------------------2---------------------------------------------

def calculate_recall(tp, fn):
    """计算召回率"""
    #------------------------------------------------------2---------------------------------------------
    return _____
    #------------------------------------------------------2---------------------------------------------

def calculate_f1(precision, recall):
    """计算F1值"""
    #------------------------------------------------------3---------------------------------------------
    return _____
    #------------------------------------------------------3---------------------------------------------

# 主计算逻辑
def main(result_path, gold_path):
    # 解析两个文件
    pred_data = parse_file(result_path)
    true_data = parse_file(gold_path)
    
    # 确保pred_data长度与true_data一致（不足补空列表）
    if len(pred_data) < len(true_data):
        pred_data += [[] for _ in range(len(true_data) - len(pred_data))]
    
    total_tp = 0
    total_fp = 0
    total_fn = 0
    
    # 计算指标
    #------------------------------------------------------4---------------------------------------------
    for preds, truths in zip(pred_data, true_data):
        pred_set = {tuple(item) for item in preds}
        truth_set = {tuple(item) for item in truths}
        
        # 计算当前行的TP/FP/FN
        tp = _____
        fp = _____
        fn = _____
        
        total_tp += tp
        total_fp += fp
        total_fn += fn
    #------------------------------------------------------4---------------------------------------------
    
    # 计算全局指标
    precision = calculate_precision(total_tp, total_fp)
    recall = calculate_recall(total_tp, total_fn)
    f1 = calculate_f1(precision, recall)
    
    # 准备结果字符串
    result_str = f"全局统计: TP={total_tp} | FP={total_fp} | FN={total_fn}\n"
    result_str += f"精确率(Precision): {precision:.4f}\n"
    result_str += f"召回率(Recall): {recall:.4f}\n"
    result_str += f"F1值: {f1:.4f}\n"
    
    # 打印结果
    print(result_str)

    # 保存结果到文件
    main_result_path = os.path.join('..', 'data', 'main_result.txt')
    with open(main_result_path, 'w', encoding='utf-8') as result_file:
        result_file.write(result_str)
    
    print("结果已保存到 main_result.txt")


if __name__ == "__main__":
    result_path = os.path.join('..', 'data', 'result_kg.txt')
    gold_path = os.path.join('..', 'data', 'target.txt')
    main(result_path,gold_path)

