# 模块A 自然语言处理技术应用 详细打分文档

## A1 命名实体提取（NER）任务 （共20分）

### M1 数据处理（convert2bioes.py） （4分）

#### 步骤1 转换文件格式 （1分）
- **M1.1** 定义split_sentences函数 （1分）
  - ①正确实现文本分割逻辑 （0.5分）
  - ②正确处理句子边界和剩余文本 （0.5分）

#### 步骤2 根据JSON格式数据划分BIOES标注数据 （1分）
- **M1.2** 定义process_entity函数 （1分）
  - ①正确处理单字实体标注（S标签） （0.5分）
  - ②正确处理多字实体标注（B-I-E标签） （0.5分）

#### 步骤3 根据特定标点符号分割文本为句子列表 （1分）
- **M1.3** 定义convert_to_bioes函数 （1分）
  - ①正确提取段落和句子信息 （0.5分）
  - ②正确调用process_entity函数生成BIOES标注 （0.5分）

#### 步骤4 完成格式转变 （1分）
- **M1.4** 定义process_directory函数 （1分）
  - ①正确遍历目录中的JSON文件 （0.5分）
  - ②正确调用convert_to_bioes函数并合并结果 （0.5分）

### M2 命名实体识别模型实现 （16分）

#### 步骤1 构建模型输入数据(data.py) （1分）
- **M2.1** build_corpus函数实现 （1分）
  - ①正确读取bioes文件并解析词和标签 （0.5分）
  - ②正确生成word2id和tag2id映射 （0.5分）

#### 步骤2 基于条件随机场（CRF）的序列标注模型 （2分）
- **M2.2** CRF模型训练和测试 （2分）
  - ①正确实现训练函数，提取特征并训练模型 （1分）
  - ②正确实现测试函数，提取特征并预测 （1分）

#### 步骤3 基于双向LSTM（BiLSTM）的序列标注模型 （2分）
- **M2.3** BiLSTM模型构建 （2分）
  - ①正确定义词向量层 （0.5分）
  - ②正确定义BiLSTM层参数 （0.5分）
  - ③正确定义输出全连接层 （0.5分）
  - ④正确实现forward函数 （0.5分）

#### 步骤4 基于隐马尔可夫模型（HMM）的序列标注模型 （4分）
- **M2.4** HMM模型参数估计和解码 （4分）
  - ①正确估计状态转移矩阵 （0.5分）
  - ②正确估计观测概率矩阵 （0.5分）
  - ③正确估计初始状态概率分布 （1分）
  - ④正确实现维特比算法递推公式 （1分）
  - ⑤正确找到最优路径终点 （0.5分）
  - ⑥正确实现路径回溯 （0.5分）

#### 步骤5 基于双向LSTM和条件随机场（BiLSTM-CRF）的序列标注模型 （4分）
- **M2.5** BiLSTM-CRF模型实现 （4分）
  - ①正确定义转移矩阵 （0.5分）
  - ②正确计算发射分数和转移分数 （0.5分）
  - ③正确实现Viterbi解码前向递推 （1分）
  - ④正确补全BiLSTM-CRF模型定义 （0.5分）
  - ⑤正确定义优化器 （0.5分）
  - ⑥正确实现前向传播和损失计算 （1分）

#### 步骤6 模型性能指标定义(evaluating.py) （2分）
- **M2.6** 评估指标计算 （2分）
  - ①正确计算精确率 （0.5分）
  - ②正确计算召回率 （0.5分）
  - ③正确计算F1分数 （1分）

#### 步骤7 模型训练和测试（evaluate.py） （1分）
- **M2.7** 模型训练评估流程 （1分）
  - ①CRF模型调用、训练、保存和测试（任选其一） （1分）
  - ②HMM模型调用、训练、保存和测试（任选其一） （1分）
  - ③BiLSTM模型调用、训练、保存和测试（任选其一） （1分）

## A2 三元组提取（KGC）任务 （共10分）

### M3 数据处理（data/raw_data_process.py） （2分）

#### 步骤1 转换文件格式 （2分）
- **M3.1** jsonl_to_txt函数实现 （2分）
  - ①正确解析JSONL文件格式 （0.5分）
  - ②正确提取text字段内容 （0.5分）
  - ③正确提取和序列化triple_list字段 （1分）

### M4 知识图谱构建（base/use_llm.py + run_llm.py） （4分）

#### 步骤1 填写文档路径参数 （0.5分）
- **M4.1** 文档路径参数填写 （0.5分）
  - ①正确填写源文件路径，并且填写了保存位置参数 （0.5分）

#### 步骤2 构造few shot示例 （1分）
- **M4.2** few shot示例构建 （1分）
  - ①正确选择示例数据格式 （0.5分）
  - ②正确构造示例字典结构 （0.5分）

#### 步骤3 构造prompt提示词 （1.5分）
- **M4.3** prompt提示词设计 （1.5分）
  - ①正确设计系统角色和任务描述 （1分）
  - ②正确整合few shot示例到提示词 （0.5分）

#### 步骤3 精度评测 （1分）
- **M5.3** 评估指标实现 （1分）
  - ②正确计算tp （0.5分）
  - ③正确计算F1分数并处理边界情况 （0.5分）

### M5 知识图谱构建与推理（base/knowledge_graph.py + kg_reasoning.py） （4分）

#### 步骤1 知识图谱构建 （2分）
- **M5.1** 图结构构建 （2分）
  - ①正确添加实体关系边到图中 （0.5分）
  - ②正确实现基于关系类型的实体邻居查找 （全对得1分）
  - ③正确实现知识图谱中的最短路径查找 （0.5分）

#### 步骤2 知识图谱推理 （2分）
- **M5.2** 推理算法实现 （2分）
  - ①正确实现基于知识图谱的疾病症状查找 （0.5分）
  - ②正确实现基于症状相似性的疾病推理（Jaccard相似度计算） （1分）
  - ③正确调用函数获取邻居列表 （0.5分）


## 总分分布
- **A1 命名实体提取（NER）**: 20分
- **A2 三元组提取（KGC）**: 10分
- **总计**: 30分

## 评分标准说明
1. 每个子任务按照代码完整性和正确性评分
2. 代码语法错误扣除相应分数
3. 逻辑错误或功能不完整按比例扣分
4. 未实现的功能不得分
5. 代码风格和注释质量作为加分项考虑
