import json
import ast
import networkx as nx
from collections import defaultdict, Counter

class KnowledgeGraph:
    """知识图谱构建和分析类"""
    
    def __init__(self):
        self.graph = nx.DiGraph()  # 有向图
        self.entity_types = defaultdict(set)  # 实体类型映射
        self.relation_stats = Counter()  # 关系统计
        
    def build_from_triples(self, triples_file_path):
        """
        从三元组文件构建知识图谱
        """
        try:
            with open(triples_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        #------------------------------------------------------1---------------------------------------------
                        # TODO: 解析三元组数据并构建知识图谱
                        # 需要：1. 解析JSON/Python格式的三元组 2. 添加实体节点和关系边到图中
                        try:
                            triples = json.loads(line)  # 解析标准JSON（双引号）
                        except json.JSONDecodeError:
                            triples = ast.literal_eval(line)  # 解析Python格式（单引号）

                        for triple in triples:
                            if len(triple) >= 3:
                                head, relation, tail = triple[0], triple[1], triple[2]

                                # 添加实体节点和关系边到知识图谱
                                self.graph.add_node(head, type='entity')
                                self.graph.add_node(tail, type='entity')
                                self.graph._____

                                # 统计关系类型
                                self.relation_stats[relation] += 1
                        #------------------------------------------------------1---------------------------------------------
                        
                    except (json.JSONDecodeError, ValueError, SyntaxError):
                        print(f"第{line_num}行JSON解析错误，跳过")
                        continue
                        
        except FileNotFoundError:
            print(f"文件不存在: {triples_file_path}")
        except Exception as e:
            print(f"构建知识图谱时出错: {e}")
    
    def get_entity_neighbors(self, entity, relation_type=None):
        """
        获取实体的邻居节点
        """
        #------------------------------------------------------2---------------------------------------------
        # TODO: 实现基于关系类型的实体邻居查找
        # 需要：遍历图中的邻居节点，根据关系类型进行筛选
        neighbors = []

        # 获取所有出边邻居（当前实体作为头实体）
        for neighbor in self.graph.successors(entity):
            edge_data = self.graph.get_edge_data(entity, neighbor)
            if relation_type is None or edge_data.get('relation') == relation_type:
                neighbors.append((neighbor, edge_data.get('relation')))

        # 获取所有入边邻居（当前实体作为尾实体）
        for neighbor in self.graph.predecessors(_____):
            edge_data = self.graph.get_edge_data(_____, entity)
            if relation_type is None or edge_data.get('relation') == _____:
                neighbors.append((neighbor, edge_data.get('relation')))

        return neighbors
        #------------------------------------------------------2---------------------------------------------
    
    def find_shortest_path(self, start_entity, end_entity):
        """
        查找两个实体之间的最短路径
        """
        try:
            #------------------------------------------------------3---------------------------------------------
            # TODO: 实现知识图谱中两实体间的最短路径查找
            # 需要：检查实体存在性，使用NetworkX算法查找路径
            if start_entity in self.graph and end_entity in self.graph:
                path = nx.shortest_path(_____, _____, _____)
                return path
            else:
                return None
            #------------------------------------------------------3---------------------------------------------
        except nx.NetworkXNoPath:
            return None
        except Exception as e:
            print(f"路径查找出错: {e}")
            return None
    
    def get_graph_statistics(self):
        """
        获取知识图谱统计信息
        """
        stats = {
            "节点数量": self.graph.number_of_nodes(),
            "边数量": self.graph.number_of_edges(),
            "关系类型数": len(self.relation_stats),
            "最常见关系": self.relation_stats.most_common(5),
            "平均度数": sum(dict(self.graph.degree()).values()) / self.graph.number_of_nodes() if self.graph.number_of_nodes() > 0 else 0
        }
        return stats
    
    def save_graph(self, output_path):
        """
        保存知识图谱到文件
        """
        graph_data = {
            "nodes": list(self.graph.nodes(data=True)),
            "edges": [(u, v, d) for u, v, d in self.graph.edges(data=True)],
            "statistics": self.get_graph_statistics()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(graph_data, f, ensure_ascii=False, indent=2)
        
        print(f"知识图谱已保存到: {output_path}")

if __name__ == "__main__":
    # 构建知识图谱
    kg = KnowledgeGraph()
    kg.build_from_triples("../data/target.txt")
    
    # 输出统计信息
    stats = kg.get_graph_statistics()
    print("知识图谱统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 保存知识图谱
    kg.save_graph("knowledge_graph.json")
    
    # 示例查询
    print("\n示例查询:")
    neighbors = kg.get_entity_neighbors("小细胞肺癌", "临床表现")
    print(f"小细胞肺癌的临床表现: {neighbors}")
