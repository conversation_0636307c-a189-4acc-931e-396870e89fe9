from os.path import join
from codecs import open


def build_map(lists):
    maps = {}
    for list_ in lists:
        for e in list_:
            if e not in maps:
                maps[e] = len(maps)
    return maps

def build_corpus(split, make_vocab=True, data_dir="./data"):
    """读取数据"""
    assert split in ['train', 'dev', 'test']

    word_lists = []
    tag_lists = []
    with open(join(data_dir, split+".bioes"), 'r', encoding='utf-8') as f:
        word_list = []
        tag_list = []
        #---------------------------------------1----------------------------------------
        for line in f:
            if line != '\n':
                word, tag = 
                word_list.append(word)
                tag_list.append(tag)
            elif (word_list!= []) and (tag_list!= [] ):
                word_lists.
                tag_lists.
                word_list = []
                tag_list = []
        #---------------------------------------1----------------------------------------

    #------------------------------------------2-----------------------------------
    # 如果make_vocab为True，还需要返回word2id和tag2id
    if make_vocab:
        word2id = 
        tag2id = 
        return word_lists, tag_lists, word2id, tag2id
    else:
        return word_lists, tag_lists
    #------------------------------------------2-----------------------------------


