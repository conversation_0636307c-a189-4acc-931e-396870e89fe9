import torch
import torch.nn as nn
from torch.nn.utils.rnn import pad_packed_sequence, pack_padded_sequence


class BiLSTM(nn.Module):
    def __init__(self, vocab_size, emb_size, hidden_size, out_size):
        """初始化参数：
            vocab_size:字典的大小
            emb_size:词向量的维数
            hidden_size：隐向量的维数
            out_size:标注的种类
        """
        super(BiLSTM, self).__init__()
        #-----------------------------------------------1--------------------------------------------
        self.embedding = 
        #-----------------------------------------------1--------------------------------------------
        #-----------------------------------------------2--------------------------------------------
        self.bilstm = nn.LSTM(emb_size, hidden_size,
                              
							  )
        #-----------------------------------------------2--------------------------------------------
        #-----------------------------------------------3--------------------------------------------
        self.lin = nn.Linear(
		
		)
        #-----------------------------------------------3--------------------------------------------

    def forward(self, sents_tensor, lengths):
        #-----------------------------------------------4--------------------------------------------
        emb = 

        packed = pack_padded_sequence(emb, lengths, batch_first=True)
        rnn_out, _ = 
        rnn_out, _ = pad_packed_sequence(rnn_out, batch_first=True)

        scores = 
        #-----------------------------------------------4--------------------------------------------
        return scores

    def test(self, sents_tensor, lengths, _):
        """第三个参数不会用到，加它是为了与BiLSTM_CRF保持同样的接口"""
        logits = self.forward(sents_tensor, lengths)  # [B, L, out_size]
        _, batch_tagids = torch.max(logits, dim=2)

        return batch_tagids
